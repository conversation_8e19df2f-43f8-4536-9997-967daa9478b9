import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/register_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/otp_verification_screen.dart';
import 'package:nextsportz_v2/core/widgets/blaze_text_form_field.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';

import '../../../../test_helpers.dart';
import 'register_screen_test.mocks.dart';

@GenerateNiceMocks([MockSpec<AuthRepository>()])
void main() {
  setUpAll(() {
    // Provide dummy values for Either types
    provideDummy<Either<AppError, void>>(const Right(null));
    provideDummy<Either<AppError, User>>(
      Right(
        User(
          id: 'test',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '**********',
          role: 'PLAYER',
          createdAt: DateTime(2023),
          updatedAt: DateTime(2023),
          isActive: true,
        ),
      ),
    );
  });

  group('RegisterScreen UI Tests', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });
    testWidgets('should render registration form with all fields', (
      tester,
    ) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Assert
      expect(find.text('Create account'), findsAtLeastNWidgets(1));
      expect(
        find.byType(BlazeTextFormField),
        findsNWidgets(4),
      ); // name, email, phone, password
      expect(
        find.byType(DropdownButtonFormField),
        findsOneWidget,
      ); // role selector
      expect(find.byType(FilledButton), findsOneWidget); // submit button
    });

    testWidgets('should show validation errors for empty fields', (
      tester,
    ) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Tap submit button without filling fields
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Enter name'), findsOneWidget);
      expect(find.text('Enter email'), findsOneWidget);
      expect(find.text('Enter phone'), findsOneWidget);
      expect(find.text('Min 6 chars'), findsOneWidget);
    });

    testWidgets('should call register method with correct parameters', (
      tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      verify(
        mockAuthRepository.register(
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '**********',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });

    testWidgets('should navigate to OTP screen on successful registration', (
      tester,
    ) async {
      // Arrange
      String? navigatedRoute;
      final router = GoRouter(
        initialLocation: '/register',
        routes: [
          GoRoute(
            path: '/register',
            builder: (context, state) => const RegisterScreen(),
          ),
          GoRoute(
            path: '/otp-verification',
            builder: (context, state) {
              navigatedRoute = '/otp-verification';
              final phoneNumber =
                  state.uri.queryParameters['phoneNumber'] ?? '';
              final role = state.uri.queryParameters['role'] ?? 'PLAYER';
              return OtpVerificationScreen(
                phoneNumber: phoneNumber,
                role: role,
              );
            },
          ),
        ],
      );

      when(
        mockAuthRepository.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...TestHelpers.createDefaultProviderOverrides(),
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: MaterialApp.router(routerConfig: router),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(navigatedRoute, equals('/otp-verification'));
      expect(find.byType(OtpVerificationScreen), findsOneWidget);
    });

    testWidgets('should show error snackbar on registration failure', (
      tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => Left(AppError('Registration failed')));

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Registration failed'), findsOneWidget);
      expect(find.byType(SnackBar), findsOneWidget);
    });

    testWidgets('should show loading state during registration', (
      tester,
    ) async {
      // Arrange - Mock a slow registration call
      when(
        mockAuthRepository.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async {
        // Simulate a delay to test loading state
        await Future.delayed(const Duration(milliseconds: 100));
        return const Right(null);
      });

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pump(); // Don't settle, so we can check loading state

      // Assert loading state is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pumpAndSettle();
    });

    testWidgets('should handle form validation and submission flow', (
      tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith(
              (ref) => AuthNotifier(mockAuthRepository),
            ),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields and submit
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert registration was called
      verify(
        mockAuthRepository.register(
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '**********',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });
  });
}
